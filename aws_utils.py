# aws_utils.py - Unstructured.io integration with IAM role support
import boto3
import os
from typing import List, Optional
import tempfile
import re
import base64
from io import BytesIO
from PIL import Image
from botocore.exceptions import NoCredentialsError, ClientError
import logging

logger = logging.getLogger(__name__)

def create_aws_session(region_name: str = None) -> boto3.Session:
    """
    Create AWS session with IAM role support.

    Priority order:
    1. IAM role (if running on EC2 with instance profile)
    2. Environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
    3. AWS credentials file
    4. Default boto3 credential chain

    Args:
        region_name: AWS region name. If None, uses AWS_REGION env var or default.

    Returns:
        boto3.Session: Configured AWS session

    Raises:
        NoCredentialsError: If no valid credentials are found
    """
    region = region_name or os.getenv('AWS_REGION', 'us-east-1')

    try:
        # Try to create session without explicit credentials first
        # This will use IAM role if available, or fall back to other methods
        session = boto3.Session(region_name=region)

        # Test the session by trying to get caller identity
        sts_client = session.client('sts')
        identity = sts_client.get_caller_identity()

        logger.info(f"AWS authentication successful. Using identity: {identity.get('Arn', 'Unknown')}")
        return session

    except NoCredentialsError:
        logger.warning("No AWS credentials found in IAM role or default chain. Trying environment variables...")

        # Fall back to explicit credentials from environment variables
        access_key = os.getenv('AWS_ACCESS_KEY_ID')
        secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')

        if access_key and secret_key:
            session = boto3.Session(
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                region_name=region
            )

            # Test the session
            sts_client = session.client('sts')
            identity = sts_client.get_caller_identity()

            logger.info(f"AWS authentication successful using environment variables. Identity: {identity.get('Arn', 'Unknown')}")
            return session
        else:
            logger.error("No AWS credentials found in environment variables either.")
            raise NoCredentialsError("Unable to locate AWS credentials. Please configure IAM role or set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.")

    except ClientError as e:
        logger.error(f"AWS authentication failed: {e}")
        raise

def get_aws_client(service_name: str, region_name: str = None):
    """
    Get AWS service client with IAM role support.

    Args:
        service_name: AWS service name (e.g., 's3', 'bedrock-runtime')
        region_name: AWS region name

    Returns:
        AWS service client
    """
    session = create_aws_session(region_name)
    return session.client(service_name)

# Define function to convert elements to text format
def elements_to_text_format(elements):
    """Convert elements to a formatted text representation"""
    if not elements:
        return ""
    
    result = []
    for element in elements:
        if hasattr(element, "text"):
            result.append(element.text)
    
    return "\n\n".join(result)

def simple_clean_whitespace(text):
    """Simple function to clean extra whitespace"""
    if not text:
        return text
    # Replace multiple spaces with a single space
    text = re.sub(r'\s+', ' ', text)
    # Trim leading/trailing whitespace
    return text.strip()

class AWSClient:
    def __init__(self, region_name: str = None):
        """
        Initialize AWS client with IAM role support.

        Args:
            region_name: AWS region name. If None, uses AWS_REGION env var.
        """
        self.region = region_name or os.getenv('AWS_REGION', 'us-east-1')

        try:
            # Create AWS session with IAM role support
            self.session = create_aws_session(self.region)
            self.s3_client = self.session.client('s3')

            logger.info("AWSClient initialized successfully with IAM role authentication")

        except Exception as e:
            logger.error(f"Failed to initialize AWSClient: {e}")
            raise

        self._init_unstructured()

    def _init_unstructured(self):
        # No API key needed for local unstructured processing
        pass

    def test_connection(self) -> dict:
        bucket_name = os.getenv('S3_BUCKET_NAME')
        self.s3_client.head_bucket(Bucket=bucket_name)
        response = self.s3_client.list_objects_v2(Bucket=bucket_name, MaxKeys=1)
        has_objects = 'Contents' in response
        return {
            "status": "success", 
            "bucket": bucket_name,
            "accessible": True,
            "has_objects": has_objects,
            "parser_status": "unstructured_initialized"
        }

    def list_s3_files(self, bucket_name: str) -> List[str]:
        response = self.s3_client.list_objects_v2(Bucket=bucket_name)
        if 'Contents' not in response:
            return []
        return [obj['Key'] for obj in response['Contents']]

    def read_s3_file(self, bucket_name: str, file_key: str) -> Optional[dict]:
        """Read and parse file from S3 bucket.
        Returns a dictionary with 'text' content and optional 'images' list."""
        response = self.s3_client.get_object(Bucket=bucket_name, Key=file_key)
        content_bytes = response['Body'].read()
        if not content_bytes:
            return {"text": "", "images": []}
        
        head_response = self.s3_client.head_object(Bucket=bucket_name, Key=file_key)
        content_type = head_response.get('ContentType', 'unknown')
        file_extension = os.path.splitext(file_key)[1].lower()
        
        if file_extension == '.pdf' or 'pdf' in content_type.lower():
            parsed_content = self._parse_pdf_with_unstructured(content_bytes, file_key)
            return {"text": parsed_content, "images": []}
        elif file_extension == '.docx' or 'officedocument.wordprocessingml.document' in content_type.lower():
            return self._parse_docx_file(content_bytes, file_key)
        else:
            text_content = self._parse_text_file(content_bytes)
            return {"text": text_content, "images": []}

    def _parse_pdf_with_unstructured(self, content_bytes: bytes, source_filename: str) -> dict:
        """Parse PDF file using Unstructured.io, including image extraction."""
        from unstructured.partition.pdf import partition_pdf
        import os
        import base64
        from io import BytesIO
        from PIL import Image
        
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_file.write(content_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Enable image extraction with extract_images_in_pdf=True
            elements = partition_pdf(
                filename=temp_file_path,
                strategy="fast",
                infer_table_structure=True,
                extract_images_in_pdf=True,
                metadata_filename=source_filename
            )
            
            # Process text elements
            text_elements = []
            image_elements = []
            
            for element in elements:
                if hasattr(element, "text"):
                    element.text = simple_clean_whitespace(element.text)
                    text_elements.append(element)
                # Check if this is an image element
                elif hasattr(element, "image") and element.image is not None:
                    # Store the image element for processing
                    image_elements.append(element)
            
            # Convert text elements to markdown
            markdown_text = elements_to_text_format(text_elements)
            
            # Process image elements
            images_data = []
            for i, img_element in enumerate(image_elements):
                try:
                    # Convert PIL Image to base64 for storage/transmission
                    img = img_element.image
                    img_buffer = BytesIO()
                    img.save(img_buffer, format=img.format if img.format else 'PNG')
                    img_str = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
                    
                    # Create image metadata
                    img_data = {
                        "id": f"{os.path.basename(source_filename)}_img_{i}",
                        "base64": img_str,
                        "format": img.format if img.format else 'PNG',
                        "metadata": {
                            "source": source_filename,
                            "page_number": getattr(img_element, "page_number", None),
                            "coordinates": getattr(img_element, "coordinates", None),
                            "caption": "Architecture diagram" if "architect" in source_filename.lower() or "diagram" in source_filename.lower() else "Image"
                        }
                    }
                    images_data.append(img_data)
                except Exception as e:
                    print(f"Error processing image {i} from {source_filename}: {e}")
            
            return {
                "text": markdown_text,
                "images": images_data
            }
            
        except Exception as e:
            print(f"Error parsing PDF with Unstructured: {e}")
            return {"text": "", "images": []}
        finally:
            os.unlink(temp_file_path)

    def _parse_text_file(self, content_bytes: bytes) -> dict:
        """Parse a plain text file."""
        text_content = content_bytes.decode('utf-8')
        return {"text": text_content, "images": []}

    def _parse_docx_file(self, content_bytes: bytes, source_filename: str) -> dict:
        """Parse DOCX file using Unstructured.io, including image extraction."""
        from unstructured.partition.docx import partition_docx
        import os
        from io import BytesIO
        from PIL import Image
        
        with tempfile.NamedTemporaryFile(suffix='.docx', delete=False) as temp_file:
            temp_file.write(content_bytes)
            temp_file_path = temp_file.name
        
        try:
            # Enable image extraction with extract_images=True
            elements = partition_docx(
                filename=temp_file_path,
                metadata_filename=source_filename,
                extract_images=True
            )
            
            # Process text elements
            text_elements = []
            image_elements = []
            
            for element in elements:
                if hasattr(element, "text"):
                    element.text = simple_clean_whitespace(element.text)
                    text_elements.append(element)
                # Check if this is an image element
                elif hasattr(element, "image") and element.image is not None:
                    # Store the image element for processing
                    image_elements.append(element)
            
            # Convert text elements to markdown
            markdown_text = elements_to_text_format(text_elements)
            
            # Process image elements
            images_data = []
            for i, img_element in enumerate(image_elements):
                try:
                    # Convert PIL Image to base64 for storage/transmission
                    img = img_element.image
                    img_buffer = BytesIO()
                    img.save(img_buffer, format=img.format if img.format else 'PNG')
                    img_str = base64.b64encode(img_buffer.getvalue()).decode('utf-8')
                    
                    # Create image metadata
                    img_data = {
                        "id": f"{os.path.basename(source_filename)}_img_{i}",
                        "base64": img_str,
                        "format": img.format if img.format else 'PNG',
                        "metadata": {
                            "source": source_filename,
                            "page_number": getattr(img_element, "page_number", None),
                            "coordinates": getattr(img_element, "coordinates", None),
                            "caption": "Architecture diagram" if "architect" in source_filename.lower() or "diagram" in source_filename.lower() else "Image"
                        }
                    }
                    images_data.append(img_data)
                except Exception as e:
                    print(f"Error processing image {i} from {source_filename}: {e}")
            
            return {
                "text": markdown_text,
                "images": images_data
            }
            
        except Exception as e:
            print(f"Error parsing DOCX with Unstructured: {e}")
            return {"text": "", "images": []}
        finally:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)