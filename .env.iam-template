# RAG Application Environment Configuration - IAM Role Version
# This template is for EC2 instances using IAM roles for AWS authentication

# =============================================================================
# AWS AUTHENTICATION CONFIGURATION
# =============================================================================

# AWS Region (Required)
AWS_REGION=ap-south-1

# IAM Role Authentication (Recommended for EC2)
# When using IAM roles, DO NOT set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY
# The application will automatically use the EC2 instance profile

# For local development or non-EC2 environments, uncomment and set these:
# AWS_ACCESS_KEY_ID=your-access-key-here
# AWS_SECRET_ACCESS_KEY=your-secret-key-here

# =============================================================================
# S3 CONFIGURATION
# =============================================================================

# S3 bucket name for document storage (Required)
S3_BUCKET_NAME=cop-sop-documents

# =============================================================================
# BEDROCK CONFIGURATION
# =============================================================================

# Bedrock embedding model ID (Required)
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v2:0

# Bedrock LLM model ID (Required)
BEDROCK_LLM_MODEL_ID=amazon.nova-micro-v1:0

# Bedrock LLM profile ARN (Optional - for custom inference profiles)
BEDROCK_LLM_PROFILE_ARN=arn:aws:bedrock:ap-south-1:337909778990:inference-profile/apac.amazon.nova-micro-v1:0

# =============================================================================
# VECTOR STORE CONFIGURATION
# =============================================================================

# Local Qdrant vector store path
QDRANT_PATH=./vector_store

# Qdrant URL (for remote Qdrant instances)
# QDRANT_URL=http://localhost:6333

# Qdrant API key (if using authentication)
# QDRANT_API_KEY=your-qdrant-api-key

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# FastAPI backend configuration
RAG_API_HOST=0.0.0.0
RAG_API_PORT=8888
RAG_API_WORKERS=2

# CORS configuration
ALLOWED_ORIGINS=*

# Chainlit frontend configuration
CHAINLIT_HOST=0.0.0.0
CHAINLIT_PORT=8000
API_BASE_URL=http://localhost:8888

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# Enable detailed AWS SDK logging (for debugging)
# AWS_SDK_DEBUG=true

# =============================================================================
# AUTHENTICATION MODE NOTES
# =============================================================================

# IAM Role Mode (Recommended for EC2):
# - Ensure your EC2 instance has an IAM role attached
# - The role should have policies for S3 and Bedrock access
# - Do not set AWS_ACCESS_KEY_ID or AWS_SECRET_ACCESS_KEY
# - More secure as no credentials are stored in files

# Access Key Mode (For local development):
# - Set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY
# - Use for local testing or non-EC2 environments
# - Less secure as credentials are stored in environment

# =============================================================================
# MIGRATION NOTES
# =============================================================================

# To migrate from access keys to IAM role:
# 1. Set up IAM role and instance profile (see IAM_ROLE_SETUP.md)
# 2. Attach instance profile to your EC2 instance
# 3. Comment out or remove AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY
# 4. Restart your application services
# 5. Test with: python test_iam_authentication.py

# To test authentication mode:
# Run: python test_iam_authentication.py
