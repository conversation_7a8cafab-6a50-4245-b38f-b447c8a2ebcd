#!/bin/bash

# quick-iam-migration.sh
# Quick script to apply IAM role authentication changes

set -euo pipefail

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

APP_DIR="/opt/chainlit_rag"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

log() { echo -e "${GREEN}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Check if running as root
if [[ $EUID -ne 0 ]]; then
    error "This script must be run as root (use sudo)"
    exit 1
fi

# Check application directory
if [[ ! -d "$APP_DIR" ]]; then
    error "Application directory $APP_DIR not found"
    exit 1
fi

log "Starting IAM role migration..."

# Create backup directory
mkdir -p "$APP_DIR/backups"

# Backup files
log "Creating backups..."
for file in aws_utils.py query.py ingest.py vectorstore_utils.py .env; do
    if [[ -f "$APP_DIR/$file" ]]; then
        cp "$APP_DIR/$file" "$APP_DIR/backups/${file}.backup.$TIMESTAMP"
        log "Backed up: $file"
    fi
done

# Stop services
log "Stopping services..."
systemctl stop rag-api rag-frontend 2>/dev/null || true

# 1. Update aws_utils.py - Add IAM role functions
log "Updating aws_utils.py..."

# Add imports after existing imports
sed -i '/from PIL import Image/a from botocore.exceptions import NoCredentialsError, ClientError\nimport logging\n\nlogger = logging.getLogger(__name__)' "$APP_DIR/aws_utils.py"

# Add IAM role functions before the existing functions
cat > /tmp/iam_functions.py << 'EOF'

def create_aws_session(region_name: str = None) -> boto3.Session:
    """Create AWS session with IAM role support."""
    region = region_name or os.getenv('AWS_REGION', 'us-east-1')
    
    try:
        # Try IAM role first
        session = boto3.Session(region_name=region)
        sts_client = session.client('sts')
        identity = sts_client.get_caller_identity()
        logger.info(f"AWS authentication successful. Using identity: {identity.get('Arn', 'Unknown')}")
        return session
        
    except NoCredentialsError:
        logger.warning("No IAM role found. Trying environment variables...")
        access_key = os.getenv('AWS_ACCESS_KEY_ID')
        secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        
        if access_key and secret_key:
            session = boto3.Session(
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key,
                region_name=region
            )
            sts_client = session.client('sts')
            identity = sts_client.get_caller_identity()
            logger.info(f"AWS authentication using environment variables. Identity: {identity.get('Arn', 'Unknown')}")
            return session
        else:
            raise NoCredentialsError("No AWS credentials found. Configure IAM role or set environment variables.")
    
    except Exception as e:
        logger.error(f"AWS authentication failed: {e}")
        raise

def get_aws_client(service_name: str, region_name: str = None):
    """Get AWS service client with IAM role support."""
    session = create_aws_session(region_name)
    return session.client(service_name)

EOF

# Insert the functions after the logger line
sed -i '/logger = logging.getLogger(__name__)/r /tmp/iam_functions.py' "$APP_DIR/aws_utils.py"

# Update AWSClient class
sed -i '/class AWSClient:/,/def _init_unstructured/ {
    /def __init__(self):/c\
    def __init__(self, region_name: str = None):\
        """Initialize AWS client with IAM role support."""\
        self.region = region_name or os.getenv('\''AWS_REGION'\'', '\''us-east-1'\'')\
        \
        try:\
            # Create AWS session with IAM role support\
            self.session = create_aws_session(self.region)\
            self.s3_client = self.session.client('\''s3'\'')\
            logger.info("AWSClient initialized successfully with IAM role authentication")\
        except Exception as e:\
            logger.error(f"Failed to initialize AWSClient: {e}")\
            raise\
        \
        self._init_unstructured()
    /self\.region = os\.getenv/d
    /self\.access_key = os\.getenv/d
    /self\.secret_key = os\.getenv/d
    /self\.s3_client = boto3\.client/,/)/d
}' "$APP_DIR/aws_utils.py"

# 2. Update query.py
log "Updating query.py..."

# Add import
sed -i '/from token_utils import/a from aws_utils import create_aws_session' "$APP_DIR/query.py"

# Add AWS session creation
sed -i '/self.client = load_vectorstore_client()/a \\n        # Create AWS session with IAM role support\n        self.aws_session = create_aws_session(os.getenv("AWS_REGION"))' "$APP_DIR/query.py"

# Remove explicit credentials from BedrockEmbeddings
sed -i '/aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),/d' "$APP_DIR/query.py"
sed -i '/aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),/d' "$APP_DIR/query.py"

# Add comment about IAM role
sed -i '/self.embeddings = BedrockEmbeddings(/a\            # Remove explicit credentials to use IAM role' "$APP_DIR/query.py"

# Remove credentials from ChatBedrock configurations
sed -i '/aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),/d' "$APP_DIR/query.py"
sed -i '/aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),/d' "$APP_DIR/query.py"

# 3. Update ingest.py
log "Updating ingest.py..."

# Remove explicit credentials
sed -i '/aws_access_key_id=os.getenv('\''AWS_ACCESS_KEY_ID'\'')/d' "$APP_DIR/ingest.py"
sed -i '/aws_secret_access_key=os.getenv('\''AWS_SECRET_ACCESS_KEY'\'')/d' "$APP_DIR/ingest.py"

# Add comment
sed -i '/self.embeddings = BedrockEmbeddings(/i\        # Initialize Bedrock embeddings with IAM role authentication' "$APP_DIR/ingest.py"
sed -i '/model_id=os.getenv/a\            # Remove explicit credentials to use IAM role authentication' "$APP_DIR/ingest.py"

# 4. Update vectorstore_utils.py
log "Updating vectorstore_utils.py..."

# Remove explicit credentials
sed -i '/aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),/d' "$APP_DIR/vectorstore_utils.py"
sed -i '/aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),/d' "$APP_DIR/vectorstore_utils.py"

# Update function documentation
sed -i 's/def get_bedrock_embeddings():/def get_bedrock_embeddings():\n    """Get Bedrock embeddings with IAM role authentication support."""/' "$APP_DIR/vectorstore_utils.py"
sed -i '/region_name=os.getenv("AWS_REGION"),/a\        # Remove explicit credentials to use IAM role authentication' "$APP_DIR/vectorstore_utils.py"

# 5. Update .env file
log "Updating environment configuration..."

# Comment out AWS credentials
sed -i 's/^AWS_ACCESS_KEY_ID=/#AWS_ACCESS_KEY_ID=/' "$APP_DIR/.env"
sed -i 's/^AWS_SECRET_ACCESS_KEY=/#AWS_SECRET_ACCESS_KEY=/' "$APP_DIR/.env"

# Add IAM role comment
cat >> "$APP_DIR/.env" << 'EOF'

# IAM Role Authentication Configuration
# AWS credentials commented out to use IAM role authentication
# Ensure your EC2 instance has proper IAM role attached with S3 and Bedrock permissions
EOF

# Set permissions
chown -R raguser:raguser "$APP_DIR"
chmod 640 "$APP_DIR/.env"

# Clean up temporary files
rm -f /tmp/iam_functions.py

# Start services
log "Starting services..."
systemctl daemon-reload
systemctl start rag-api rag-frontend

# Wait and check status
sleep 3
if systemctl is-active --quiet rag-api && systemctl is-active --quiet rag-frontend; then
    log "✅ Services started successfully"
else
    warn "⚠️  Some services may have issues. Check with: sudo systemctl status rag-api rag-frontend"
fi

log "✅ IAM role migration completed!"
log "📁 Backups saved in: $APP_DIR/backups/"
log "🔍 Test authentication with: aws sts get-caller-identity"
log "🚀 Your application now uses IAM role authentication"

echo ""
echo "Next steps:"
echo "1. Verify IAM role is attached: curl http://***************/latest/meta-data/iam/security-credentials/"
echo "2. Test AWS access: aws sts get-caller-identity"
echo "3. Test application: curl -X POST http://localhost:8888/ingest"
echo "4. Check logs: sudo journalctl -u rag-api -f"
