# Deploy IAM Changes to EC2 Instance

## Method 1: Quick Migration Script (Recommended)

### Step 1: Upload the Script to Your EC2 Instance

```bash
# From your local machine, upload the script
scp -i your-key.pem quick-iam-migration.sh ec2-user@YOUR_EC2_IP:/tmp/

# SSH into your EC2 instance
ssh -i your-key.pem ec2-user@YOUR_EC2_IP
```

### Step 2: Run the Migration Script

```bash
# Make the script executable
chmod +x /tmp/quick-iam-migration.sh

# Run the migration (requires sudo)
sudo /tmp/quick-iam-migration.sh
```

### Step 3: Verify the Changes

```bash
# Check IAM role is attached
curl http://***************/latest/meta-data/iam/security-credentials/

# Test AWS authentication
aws sts get-caller-identity

# Test your application
curl -X POST http://localhost:8888/ingest

# Check service status
sudo systemctl status rag-api rag-frontend
```

## Method 2: Manual File-by-File Changes

If you prefer to make changes manually, here are the exact commands:

### Step 1: Backup Original Files

```bash
sudo mkdir -p /opt/chainlit_rag/backups
sudo cp /opt/chainlit_rag/aws_utils.py /opt/chainlit_rag/backups/aws_utils.py.backup
sudo cp /opt/chainlit_rag/query.py /opt/chainlit_rag/backups/query.py.backup
sudo cp /opt/chainlit_rag/ingest.py /opt/chainlit_rag/backups/ingest.py.backup
sudo cp /opt/chainlit_rag/vectorstore_utils.py /opt/chainlit_rag/backups/vectorstore_utils.py.backup
sudo cp /opt/chainlit_rag/.env /opt/chainlit_rag/backups/.env.backup
```

### Step 2: Stop Services

```bash
sudo systemctl stop rag-api rag-frontend
```

### Step 3: Update Environment File

```bash
# Comment out AWS credentials
sudo sed -i 's/^AWS_ACCESS_KEY_ID=/#AWS_ACCESS_KEY_ID=/' /opt/chainlit_rag/.env
sudo sed -i 's/^AWS_SECRET_ACCESS_KEY=/#AWS_SECRET_ACCESS_KEY=/' /opt/chainlit_rag/.env

# Add IAM role comment
echo "" | sudo tee -a /opt/chainlit_rag/.env
echo "# IAM Role Authentication - Credentials commented out" | sudo tee -a /opt/chainlit_rag/.env
```

### Step 4: Update Python Files

Upload the modified files from your local machine:

```bash
# From local machine, upload modified files
scp -i your-key.pem aws_utils.py ec2-user@YOUR_EC2_IP:/tmp/
scp -i your-key.pem query.py ec2-user@YOUR_EC2_IP:/tmp/
scp -i your-key.pem ingest.py ec2-user@YOUR_EC2_IP:/tmp/
scp -i your-key.pem vectorstore_utils.py ec2-user@YOUR_EC2_IP:/tmp/

# On EC2 instance, move files to application directory
sudo mv /tmp/aws_utils.py /opt/chainlit_rag/
sudo mv /tmp/query.py /opt/chainlit_rag/
sudo mv /tmp/ingest.py /opt/chainlit_rag/
sudo mv /tmp/vectorstore_utils.py /opt/chainlit_rag/

# Set proper ownership
sudo chown raguser:raguser /opt/chainlit_rag/*.py
sudo chown raguser:raguser /opt/chainlit_rag/.env
sudo chmod 640 /opt/chainlit_rag/.env
```

### Step 5: Start Services

```bash
sudo systemctl daemon-reload
sudo systemctl start rag-api rag-frontend
sudo systemctl status rag-api rag-frontend
```

## Method 3: One-Line Commands for Each File

### Update aws_utils.py

```bash
# Add IAM role authentication functions
sudo tee -a /opt/chainlit_rag/aws_utils_iam.py << 'EOF'
# Add the complete aws_utils.py content with IAM role support
# (This would be the full file content)
EOF

# Replace the original file
sudo mv /opt/chainlit_rag/aws_utils_iam.py /opt/chainlit_rag/aws_utils.py
```

### Update query.py

```bash
# Remove explicit credentials from query.py
sudo sed -i '/aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID")/d' /opt/chainlit_rag/query.py
sudo sed -i '/aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY")/d' /opt/chainlit_rag/query.py

# Add import
sudo sed -i '/from token_utils import/a from aws_utils import create_aws_session' /opt/chainlit_rag/query.py
```

### Update ingest.py

```bash
# Remove explicit credentials from ingest.py
sudo sed -i '/aws_access_key_id=os.getenv/d' /opt/chainlit_rag/ingest.py
sudo sed -i '/aws_secret_access_key=os.getenv/d' /opt/chainlit_rag/ingest.py
```

### Update vectorstore_utils.py

```bash
# Remove explicit credentials from vectorstore_utils.py
sudo sed -i '/aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID")/d' /opt/chainlit_rag/vectorstore_utils.py
sudo sed -i '/aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY")/d' /opt/chainlit_rag/vectorstore_utils.py
```

## Verification Commands

After applying any method, verify the changes:

```bash
# 1. Check IAM role attachment
curl http://***************/latest/meta-data/iam/security-credentials/

# 2. Test AWS CLI authentication
aws sts get-caller-identity

# 3. Test S3 access
aws s3 ls s3://cop-sop-documents

# 4. Check application services
sudo systemctl status rag-api rag-frontend

# 5. Test application endpoints
curl -X POST http://localhost:8888/ingest
curl http://localhost:8888/health

# 6. Check application logs
sudo journalctl -u rag-api -n 20
sudo journalctl -u rag-frontend -n 20
```

## Rollback Instructions

If something goes wrong, you can rollback:

```bash
# Stop services
sudo systemctl stop rag-api rag-frontend

# Restore from backups
sudo cp /opt/chainlit_rag/backups/*.backup /opt/chainlit_rag/

# Restart services
sudo systemctl start rag-api rag-frontend
```

## Troubleshooting

### If services fail to start:

```bash
# Check service logs
sudo journalctl -u rag-api -f
sudo journalctl -u rag-frontend -f

# Check Python import errors
cd /opt/chainlit_rag
sudo -u raguser python3 -c "from aws_utils import create_aws_session; print('Success')"
```

### If authentication fails:

```bash
# Verify IAM role
curl http://***************/latest/meta-data/iam/security-credentials/

# Check if credentials are still in environment
grep -E "^AWS_ACCESS_KEY_ID|^AWS_SECRET_ACCESS_KEY" /opt/chainlit_rag/.env

# Test with explicit credentials temporarily
export AWS_ACCESS_KEY_ID=your-key
export AWS_SECRET_ACCESS_KEY=your-secret
aws sts get-caller-identity
```

**Recommendation: Use Method 1 (Quick Migration Script) as it's the safest and most comprehensive approach.**
