import os
from typing import List
from dotenv import load_dotenv
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_aws import BedrockEmbeddings
from langchain.schema import Document
from aws_utils import AWSClient
from vectorstore_utils import build_vectorstore, load_vectorstore
import glob
import uuid

load_dotenv()

class DocumentWithID(Document):
    @property
    def id(self):
        return self.metadata.get('id')

class DocumentIngester:
    def __init__(self):
        self.aws_client = AWSClient()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1024,
            chunk_overlap=200,
            length_function=len,
            separators=["\n\n", "\n", ".", "!", "?", ";", ":", ",", " ", ""]
        )
        
        # Initialize Bedrock embeddings with IAM role authentication
        self.embeddings = BedrockEmbeddings(
            region_name=os.getenv('AWS_REGION'),
            # Remove explicit credentials to use IAM role authentication
            model_id=os.getenv('BEDROCK_EMBEDDING_MODEL_ID')
        )
        
        self.vector_store = None

    def process_s3_documents(self) -> dict:
        bucket_name = os.getenv('S3_BUCKET_NAME')
        files = self.aws_client.list_s3_files(bucket_name)
        
        if not files:
            return {"status": "success", "message": "No documents found in S3 bucket", "chunks": "0"}
        
        documents: List[Document] = []
        image_documents: List[Document] = []
        
        for file_key in files:
            print(f"Processing file: {file_key}")
            content_dict = self.aws_client.read_s3_file(bucket_name, file_key)
            
            if not content_dict:
                continue
            
            text_content = content_dict.get("text", "")
            images = content_dict.get("images", [])
            
            if text_content and text_content.strip():
                short_source = os.path.basename(file_key)
                file_extension = os.path.splitext(file_key)[1].lower()
                
                # Add metadata to help with source attribution
                metadata = {
                    "source": short_source,
                    "file_type": file_extension.replace('.', ''),
                    "file_path": file_key
                }
                
                doc = Document(page_content=text_content.strip(), metadata=metadata)
                documents.append(doc)
            
            # Process any extracted images
            for img_data in images:
                img_metadata = {
                    "source": img_data["id"],
                    "file_type": "image",
                    "image_format": img_data.get("format", "PNG"),
                    "file_path": file_key,
                    "is_image": True,
                    "base64_image": img_data["base64"],
                    "image_caption": img_data["metadata"].get("caption", "Image"),
                    "page_number": img_data["metadata"].get("page_number"),
                }
                
                # Create a document for each image with minimal text content for retrieval
                image_caption = img_data["metadata"].get("caption", "Image")
                doc_content = f"This is an {image_caption} from document {file_key}."
                
                img_doc = Document(page_content=doc_content, metadata=img_metadata)
                image_documents.append(img_doc)
        
        all_documents = documents + image_documents
        
        if not all_documents:
            return {"status": "success", "message": "No valid documents found to process", "chunks": "0"}
        
        # Process documents into chunks
        print(f"Splitting {len(documents)} text documents and {len(image_documents)} images into chunks...")
        chunks = self.text_splitter.split_documents(all_documents)
        
        # Add unique IDs to each chunk
        for chunk in chunks:
            chunk.metadata['id'] = str(uuid.uuid4())
            
            # Keep track of chunk position for better context
            if 'chunk_id' not in chunk.metadata:
                chunk.metadata['chunk_id'] = str(uuid.uuid4())
        
        chunks_with_id = [DocumentWithID(page_content=chunk.page_content, metadata=chunk.metadata) for chunk in chunks]
        
        print(f"Building vector store with {len(chunks_with_id)} chunks...")
        self.vector_store = build_vectorstore(chunks_with_id)
        
        return {
            "status": "success",
            "message": f"Processed {len(documents)} text documents and {len(image_documents)} images into {len(chunks)} chunks",
            "chunks": str(len(chunks))
        }

    def get_vector_store(self):
        if not self.vector_store:
            self.vector_store = load_vectorstore()
        return self.vector_store

    def get_vector_store_client(self):
        from vectorstore_utils import load_vectorstore_client
        return load_vectorstore_client()

if __name__ == "__main__":
    ingester = DocumentIngester()
    print("--- S3 Document Ingestion ---")
    s3_result = ingester.process_s3_documents()
    print(s3_result)
    