#!/bin/bash

# migrate-to-iam-role.sh
# Migration script to convert RAG application from access key to IAM role authentication

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/opt/chainlit_rag"
ENV_FILE="$APP_DIR/.env"
BACKUP_DIR="$APP_DIR/backups"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Check if application directory exists
check_app_directory() {
    if [[ ! -d "$APP_DIR" ]]; then
        error "Application directory $APP_DIR not found"
        error "Please ensure the RAG application is installed"
        exit 1
    fi
}

# Create backup directory
create_backup_dir() {
    mkdir -p "$BACKUP_DIR"
    log "Created backup directory: $BACKUP_DIR"
}

# Backup current configuration
backup_configuration() {
    log "Backing up current configuration..."
    
    if [[ -f "$ENV_FILE" ]]; then
        cp "$ENV_FILE" "$BACKUP_DIR/.env.backup.$TIMESTAMP"
        info "Environment file backed up to: $BACKUP_DIR/.env.backup.$TIMESTAMP"
    fi
    
    # Backup systemd service files
    for service in rag-api rag-frontend; do
        if [[ -f "/etc/systemd/system/${service}.service" ]]; then
            cp "/etc/systemd/system/${service}.service" "$BACKUP_DIR/${service}.service.backup.$TIMESTAMP"
            info "Service file ${service}.service backed up"
        fi
    done
}

# Check current authentication method
check_current_auth() {
    log "Checking current authentication method..."
    
    if [[ ! -f "$ENV_FILE" ]]; then
        error "Environment file not found: $ENV_FILE"
        exit 1
    fi
    
    # Check if access keys are currently set
    if grep -q "^AWS_ACCESS_KEY_ID=" "$ENV_FILE" && grep -q "^AWS_SECRET_ACCESS_KEY=" "$ENV_FILE"; then
        info "Current authentication: Access Keys"
        return 0
    else
        warn "Access keys not found in environment file"
        warn "Application may already be using IAM role authentication"
        return 1
    fi
}

# Check IAM role configuration
check_iam_role() {
    log "Checking IAM role configuration..."
    
    # Check if instance has IAM role attached
    local role_name
    role_name=$(curl -s http://169.254.169.254/latest/meta-data/iam/security-credentials/ 2>/dev/null || echo "")
    
    if [[ -n "$role_name" ]]; then
        info "IAM role detected: $role_name"
        
        # Test AWS credentials
        if sudo -u raguser aws sts get-caller-identity >/dev/null 2>&1; then
            info "IAM role authentication working"
            return 0
        else
            error "IAM role attached but authentication failed"
            return 1
        fi
    else
        error "No IAM role attached to this EC2 instance"
        error "Please attach an IAM role before running this migration"
        error "See IAM_ROLE_SETUP.md for instructions"
        return 1
    fi
}

# Stop services
stop_services() {
    log "Stopping RAG application services..."
    
    for service in rag-api rag-frontend; do
        if systemctl is-active --quiet "$service"; then
            systemctl stop "$service"
            info "Stopped $service"
        else
            warn "$service is not running"
        fi
    done
}

# Start services
start_services() {
    log "Starting RAG application services..."
    
    systemctl daemon-reload
    
    for service in rag-api rag-frontend; do
        systemctl start "$service"
        if systemctl is-active --quiet "$service"; then
            info "Started $service"
        else
            error "Failed to start $service"
            systemctl status "$service" --no-pager
        fi
    done
}

# Update environment configuration
update_environment() {
    log "Updating environment configuration for IAM role authentication..."
    
    # Create temporary file with updated configuration
    local temp_file=$(mktemp)
    
    # Process the environment file
    while IFS= read -r line; do
        if [[ $line =~ ^AWS_ACCESS_KEY_ID= ]]; then
            echo "# $line  # Commented out for IAM role authentication"
        elif [[ $line =~ ^AWS_SECRET_ACCESS_KEY= ]]; then
            echo "# $line  # Commented out for IAM role authentication"
        else
            echo "$line"
        fi
    done < "$ENV_FILE" > "$temp_file"
    
    # Add IAM role authentication comment
    cat >> "$temp_file" << 'EOF'

# IAM Role Authentication Configuration
# The application now uses IAM role authentication instead of access keys
# Ensure your EC2 instance has the proper IAM role attached with S3 and Bedrock permissions
EOF
    
    # Replace the original file
    mv "$temp_file" "$ENV_FILE"
    chown raguser:raguser "$ENV_FILE"
    chmod 640 "$ENV_FILE"
    
    info "Environment configuration updated for IAM role authentication"
}

# Test authentication
test_authentication() {
    log "Testing IAM role authentication..."
    
    # Run the authentication test script
    if [[ -f "$APP_DIR/test_iam_authentication.py" ]]; then
        cd "$APP_DIR"
        if sudo -u raguser "$APP_DIR/venv/bin/python" test_iam_authentication.py; then
            info "IAM role authentication test passed"
            return 0
        else
            error "IAM role authentication test failed"
            return 1
        fi
    else
        warn "Authentication test script not found, skipping automated test"
        warn "Please run 'python test_iam_authentication.py' manually to verify"
        return 0
    fi
}

# Rollback function
rollback() {
    error "Migration failed. Rolling back changes..."
    
    # Restore environment file
    if [[ -f "$BACKUP_DIR/.env.backup.$TIMESTAMP" ]]; then
        cp "$BACKUP_DIR/.env.backup.$TIMESTAMP" "$ENV_FILE"
        chown raguser:raguser "$ENV_FILE"
        info "Environment file restored from backup"
    fi
    
    # Restart services
    start_services
    
    error "Rollback completed. Application restored to previous state."
}

# Main migration function
migrate() {
    log "Starting migration to IAM role authentication..."
    
    # Pre-migration checks
    check_root
    check_app_directory
    create_backup_dir
    backup_configuration
    
    # Check current state
    if ! check_current_auth; then
        warn "Migration may not be necessary"
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "Migration cancelled"
            exit 0
        fi
    fi
    
    # Check IAM role
    if ! check_iam_role; then
        error "IAM role not properly configured"
        exit 1
    fi
    
    # Perform migration
    stop_services
    update_environment
    start_services
    
    # Test the migration
    sleep 5  # Give services time to start
    if test_authentication; then
        info "Migration completed successfully!"
        log "Your RAG application is now using IAM role authentication"
        log "Backup files are available in: $BACKUP_DIR"
    else
        rollback
        exit 1
    fi
}

# Show usage
usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  migrate    Migrate from access keys to IAM role authentication"
    echo "  test       Test current authentication method"
    echo "  rollback   Rollback to previous configuration"
    echo "  help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  sudo $0 migrate    # Migrate to IAM role authentication"
    echo "  sudo $0 test       # Test current authentication"
}

# Handle rollback command
rollback_to_backup() {
    log "Rolling back to previous configuration..."
    
    # Find the most recent backup
    local latest_backup
    latest_backup=$(ls -t "$BACKUP_DIR"/.env.backup.* 2>/dev/null | head -n1)
    
    if [[ -n "$latest_backup" ]]; then
        stop_services
        cp "$latest_backup" "$ENV_FILE"
        chown raguser:raguser "$ENV_FILE"
        start_services
        info "Rolled back to: $(basename "$latest_backup")"
    else
        error "No backup files found in $BACKUP_DIR"
        exit 1
    fi
}

# Main script logic
case "${1:-help}" in
    migrate)
        migrate
        ;;
    test)
        check_app_directory
        test_authentication
        ;;
    rollback)
        check_root
        check_app_directory
        rollback_to_backup
        ;;
    help|--help|-h)
        usage
        ;;
    *)
        error "Unknown command: $1"
        usage
        exit 1
        ;;
esac
