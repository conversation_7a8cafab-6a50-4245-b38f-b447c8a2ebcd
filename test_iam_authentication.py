#!/usr/bin/env python3
"""
IAM Role Authentication Test Script for RAG Application

This script tests IAM role-based authentication for AWS services used by the RAG application:
- S3 bucket access for document storage
- Bedrock access for LLM and embedding services

Usage:
    python test_iam_authentication.py

Requirements:
    - boto3
    - langchain-aws
    - Running on EC2 instance with proper IAM role attached
"""

import os
import sys
import boto3
import logging
from typing import Dict, Any
from dotenv import load_dotenv
from botocore.exceptions import NoCredentialsError, ClientError, BotoCoreError

# Import our custom AWS utilities
try:
    from aws_utils import create_aws_session, get_aws_client
except ImportError:
    print("❌ Error: Could not import aws_utils. Make sure you're running from the RAG application directory.")
    sys.exit(1)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class IAMAuthenticationTester:
    """Test IAM role authentication for AWS services."""
    
    def __init__(self):
        self.region = os.getenv('AWS_REGION', 'us-east-1')
        self.s3_bucket = os.getenv('S3_BUCKET_NAME')
        self.bedrock_embedding_model = os.getenv('BEDROCK_EMBEDDING_MODEL_ID')
        self.bedrock_llm_model = os.getenv('BEDROCK_LLM_MODEL_ID')
        self.results = {}
        
    def test_aws_session_creation(self) -> bool:
        """Test AWS session creation with IAM role."""
        print("\n🔍 Testing AWS Session Creation...")
        
        try:
            session = create_aws_session(self.region)
            
            # Get caller identity to verify authentication
            sts_client = session.client('sts')
            identity = sts_client.get_caller_identity()
            
            print(f"✅ AWS Session created successfully")
            print(f"   Account: {identity.get('Account', 'Unknown')}")
            print(f"   User/Role ARN: {identity.get('Arn', 'Unknown')}")
            print(f"   User ID: {identity.get('UserId', 'Unknown')}")
            
            # Check if using IAM role (ARN contains 'role/')
            arn = identity.get('Arn', '')
            if 'role/' in arn:
                print(f"   ✅ Using IAM Role authentication")
            elif 'user/' in arn:
                print(f"   ⚠️  Using IAM User authentication (consider switching to IAM role)")
            else:
                print(f"   ❓ Unknown authentication type")
                
            self.results['session_creation'] = True
            return True
            
        except NoCredentialsError as e:
            print(f"❌ No AWS credentials found: {e}")
            self.results['session_creation'] = False
            return False
        except ClientError as e:
            print(f"❌ AWS client error: {e}")
            self.results['session_creation'] = False
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            self.results['session_creation'] = False
            return False
    
    def test_s3_access(self) -> bool:
        """Test S3 bucket access."""
        print("\n🗂️  Testing S3 Access...")
        
        if not self.s3_bucket:
            print("❌ S3_BUCKET_NAME not configured in environment")
            self.results['s3_access'] = False
            return False
        
        try:
            s3_client = get_aws_client('s3', self.region)
            
            # Test bucket access
            print(f"   Testing access to bucket: {self.s3_bucket}")
            
            # Check if bucket exists and is accessible
            s3_client.head_bucket(Bucket=self.s3_bucket)
            print(f"   ✅ Bucket access successful")
            
            # List objects in bucket
            response = s3_client.list_objects_v2(Bucket=self.s3_bucket, MaxKeys=5)
            object_count = response.get('KeyCount', 0)
            print(f"   📄 Found {object_count} objects in bucket")
            
            if object_count > 0:
                print("   Sample objects:")
                for obj in response.get('Contents', [])[:3]:
                    print(f"     - {obj['Key']} ({obj['Size']} bytes)")
            
            self.results['s3_access'] = True
            return True
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'NoSuchBucket':
                print(f"❌ Bucket '{self.s3_bucket}' does not exist")
            elif error_code == 'AccessDenied':
                print(f"❌ Access denied to bucket '{self.s3_bucket}'. Check IAM permissions.")
            else:
                print(f"❌ S3 error ({error_code}): {e}")
            self.results['s3_access'] = False
            return False
        except Exception as e:
            print(f"❌ Unexpected S3 error: {e}")
            self.results['s3_access'] = False
            return False
    
    def test_bedrock_access(self) -> bool:
        """Test Bedrock service access."""
        print("\n🧠 Testing Bedrock Access...")
        
        try:
            bedrock_client = get_aws_client('bedrock-runtime', self.region)
            
            # Test with a simple embedding request
            if self.bedrock_embedding_model:
                print(f"   Testing embedding model: {self.bedrock_embedding_model}")
                
                test_text = "This is a test for IAM authentication."
                
                try:
                    response = bedrock_client.invoke_model(
                        modelId=self.bedrock_embedding_model,
                        body='{"inputText": "' + test_text + '"}',
                        contentType='application/json'
                    )
                    print(f"   ✅ Embedding model access successful")
                    
                except ClientError as e:
                    error_code = e.response['Error']['Code']
                    if error_code == 'AccessDeniedException':
                        print(f"   ❌ Access denied to embedding model. Check IAM permissions.")
                    elif error_code == 'ValidationException':
                        print(f"   ⚠️  Model accessible but validation error (expected for test)")
                    else:
                        print(f"   ❌ Embedding model error ({error_code}): {e}")
                        
            else:
                print("   ⚠️  BEDROCK_EMBEDDING_MODEL_ID not configured")
            
            # Test LLM model access
            if self.bedrock_llm_model:
                print(f"   Testing LLM model: {self.bedrock_llm_model}")
                
                try:
                    # Simple test - just check if we can access the model
                    response = bedrock_client.invoke_model(
                        modelId=self.bedrock_llm_model,
                        body='{"prompt": "Test", "max_tokens": 1}',
                        contentType='application/json'
                    )
                    print(f"   ✅ LLM model access successful")
                    
                except ClientError as e:
                    error_code = e.response['Error']['Code']
                    if error_code == 'AccessDeniedException':
                        print(f"   ❌ Access denied to LLM model. Check IAM permissions.")
                    elif error_code == 'ValidationException':
                        print(f"   ⚠️  Model accessible but validation error (expected for test)")
                    else:
                        print(f"   ❌ LLM model error ({error_code}): {e}")
                        
            else:
                print("   ⚠️  BEDROCK_LLM_MODEL_ID not configured")
            
            self.results['bedrock_access'] = True
            return True
            
        except ClientError as e:
            print(f"❌ Bedrock client error: {e}")
            self.results['bedrock_access'] = False
            return False
        except Exception as e:
            print(f"❌ Unexpected Bedrock error: {e}")
            self.results['bedrock_access'] = False
            return False
    
    def test_langchain_integration(self) -> bool:
        """Test LangChain AWS integration with IAM role."""
        print("\n🔗 Testing LangChain AWS Integration...")
        
        try:
            from langchain_aws import BedrockEmbeddings, ChatBedrock
            
            # Test BedrockEmbeddings
            if self.bedrock_embedding_model:
                print(f"   Testing BedrockEmbeddings with model: {self.bedrock_embedding_model}")
                
                embeddings = BedrockEmbeddings(
                    model_id=self.bedrock_embedding_model,
                    region_name=self.region
                    # No explicit credentials - should use IAM role
                )
                
                # Test embedding a simple text
                test_text = "IAM role authentication test"
                try:
                    embedding_vector = embeddings.embed_query(test_text)
                    print(f"   ✅ BedrockEmbeddings working (vector length: {len(embedding_vector)})")
                except Exception as e:
                    print(f"   ❌ BedrockEmbeddings error: {e}")
                    
            # Test ChatBedrock
            if self.bedrock_llm_model:
                print(f"   Testing ChatBedrock with model: {self.bedrock_llm_model}")
                
                llm = ChatBedrock(
                    model_id=self.bedrock_llm_model,
                    region_name=self.region,
                    model_kwargs={"max_tokens": 10, "temperature": 0.1}
                    # No explicit credentials - should use IAM role
                )
                
                try:
                    response = llm.invoke("Hello")
                    print(f"   ✅ ChatBedrock working (response: {response.content[:50]}...)")
                except Exception as e:
                    print(f"   ❌ ChatBedrock error: {e}")
            
            self.results['langchain_integration'] = True
            return True
            
        except ImportError as e:
            print(f"❌ LangChain import error: {e}")
            self.results['langchain_integration'] = False
            return False
        except Exception as e:
            print(f"❌ Unexpected LangChain error: {e}")
            self.results['langchain_integration'] = False
            return False
    
    def test_rag_components(self) -> bool:
        """Test RAG application components."""
        print("\n🤖 Testing RAG Application Components...")
        
        try:
            # Test AWSClient
            from aws_utils import AWSClient
            
            print("   Testing AWSClient initialization...")
            aws_client = AWSClient(self.region)
            
            # Test connection
            connection_result = aws_client.test_connection()
            if connection_result.get('status') == 'success':
                print(f"   ✅ AWSClient connection successful")
                print(f"      Bucket: {connection_result.get('bucket')}")
                print(f"      Has objects: {connection_result.get('has_objects')}")
            else:
                print(f"   ❌ AWSClient connection failed")
                
            self.results['rag_components'] = True
            return True
            
        except Exception as e:
            print(f"❌ RAG components error: {e}")
            self.results['rag_components'] = False
            return False
    
    def print_summary(self):
        """Print test summary."""
        print("\n" + "="*60)
        print("🏁 IAM AUTHENTICATION TEST SUMMARY")
        print("="*60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if result)
        
        for test_name, result in self.results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name.replace('_', ' ').title()}: {status}")
        
        print(f"\nOverall: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print("\n🎉 All tests passed! IAM role authentication is working correctly.")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please check IAM configuration.")
            print("\nTroubleshooting tips:")
            print("1. Verify IAM role is attached to EC2 instance")
            print("2. Check IAM policies include required permissions")
            print("3. Ensure environment variables are configured correctly")
            print("4. Review application logs for detailed error messages")

def main():
    """Main test function."""
    print("🚀 Starting IAM Role Authentication Tests for RAG Application")
    print("="*60)
    
    tester = IAMAuthenticationTester()
    
    # Run all tests
    tester.test_aws_session_creation()
    tester.test_s3_access()
    tester.test_bedrock_access()
    tester.test_langchain_integration()
    tester.test_rag_components()
    
    # Print summary
    tester.print_summary()

if __name__ == "__main__":
    main()
