# IAM Role Setup for RAG Application

This document provides step-by-step instructions for configuring IAM roles and policies to enable your EC2-hosted RAG application to securely access AWS services without hardcoded credentials.

## Overview

The RAG application requires access to:
- **Amazon S3**: For document storage and retrieval
- **Amazon Bedrock**: For LLM and embedding services

## Prerequisites

- AWS CLI configured with administrative privileges
- EC2 instance running your RAG application
- Basic understanding of AWS IAM concepts

## Step 1: Create IAM Policy for S3 Access

Create a policy that grants the necessary S3 permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "S3BucketAccess",
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket",
                "s3:GetBucketLocation",
                "s3:HeadBucket",
                "s3:HeadObject"
            ],
            "Resource": [
                "arn:aws:s3:::YOUR_BUCKET_NAME",
                "arn:aws:s3:::YOUR_BUCKET_NAME/*"
            ]
        }
    ]
}
```

**AWS CLI Command:**
```bash
# Replace YOUR_BUCKET_NAME with your actual S3 bucket name
aws iam create-policy \
    --policy-name RAG-S3-Access-Policy \
    --policy-document file://s3-policy.json \
    --description "S3 access policy for RAG application"
```

## Step 2: Create IAM Policy for Bedrock Access

Create a policy for Amazon Bedrock services:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "BedrockModelAccess",
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": [
                "arn:aws:bedrock:*::foundation-model/amazon.titan-embed-text-v1",
                "arn:aws:bedrock:*::foundation-model/amazon.titan-embed-text-v2:0",
                "arn:aws:bedrock:*::foundation-model/amazon.nova-micro-v1:0",
                "arn:aws:bedrock:*::foundation-model/*"
            ]
        },
        {
            "Sid": "BedrockInferenceProfileAccess",
            "Effect": "Allow",
            "Action": [
                "bedrock:InvokeModel",
                "bedrock:InvokeModelWithResponseStream"
            ],
            "Resource": "arn:aws:bedrock:*:*:inference-profile/*"
        }
    ]
}
```

**AWS CLI Command:**
```bash
aws iam create-policy \
    --policy-name RAG-Bedrock-Access-Policy \
    --policy-document file://bedrock-policy.json \
    --description "Bedrock access policy for RAG application"
```

## Step 3: Create IAM Role for EC2

Create an IAM role that can be assumed by EC2 instances:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "ec2.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
```

**AWS CLI Command:**
```bash
aws iam create-role \
    --role-name RAG-EC2-Role \
    --assume-role-policy-document file://trust-policy.json \
    --description "IAM role for RAG application EC2 instance"
```

## Step 4: Attach Policies to Role

Attach the created policies to the IAM role:

```bash
# Get the policy ARNs (replace ACCOUNT_ID with your AWS account ID)
S3_POLICY_ARN="arn:aws:iam::ACCOUNT_ID:policy/RAG-S3-Access-Policy"
BEDROCK_POLICY_ARN="arn:aws:iam::ACCOUNT_ID:policy/RAG-Bedrock-Access-Policy"

# Attach policies to the role
aws iam attach-role-policy \
    --role-name RAG-EC2-Role \
    --policy-arn $S3_POLICY_ARN

aws iam attach-role-policy \
    --role-name RAG-EC2-Role \
    --policy-arn $BEDROCK_POLICY_ARN
```

## Step 5: Create Instance Profile

Create an instance profile and add the role to it:

```bash
# Create instance profile
aws iam create-instance-profile \
    --instance-profile-name RAG-EC2-Instance-Profile

# Add role to instance profile
aws iam add-role-to-instance-profile \
    --instance-profile-name RAG-EC2-Instance-Profile \
    --role-name RAG-EC2-Role
```

## Step 6: Attach Instance Profile to EC2

Attach the instance profile to your EC2 instance:

```bash
# Replace i-1234567890abcdef0 with your actual instance ID
aws ec2 associate-iam-instance-profile \
    --instance-id i-1234567890abcdef0 \
    --iam-instance-profile Name=RAG-EC2-Instance-Profile
```

## Step 7: Verify IAM Role Configuration

After attaching the instance profile, verify the configuration:

```bash
# SSH into your EC2 instance and run:
curl http://169.254.169.254/latest/meta-data/iam/security-credentials/

# This should return the role name: RAG-EC2-Role
```

## Step 8: Update Environment Configuration

Update your application's environment configuration to support IAM role authentication:

1. **Remove or comment out AWS credentials** from your `.env` file:
```bash
# AWS Configuration
# AWS_ACCESS_KEY_ID=your-access-key  # Comment out or remove
# AWS_SECRET_ACCESS_KEY=your-secret  # Comment out or remove
AWS_REGION=ap-south-1

# S3 Configuration
S3_BUCKET_NAME=your-bucket-name

# Bedrock Configuration
BEDROCK_EMBEDDING_MODEL_ID=amazon.titan-embed-text-v2:0
BEDROCK_LLM_MODEL_ID=amazon.nova-micro-v1:0
BEDROCK_LLM_PROFILE_ARN=arn:aws:bedrock:ap-south-1:337909778990:inference-profile/apac.amazon.nova-micro-v1:0
```

2. **Restart your RAG application services**:
```bash
sudo systemctl restart rag-api
sudo systemctl restart rag-frontend
```

## Troubleshooting

### Common Issues

1. **"Unable to locate credentials" error**:
   - Verify the instance profile is attached to your EC2 instance
   - Check that the IAM role has the correct policies attached
   - Ensure the application is running on the EC2 instance with the instance profile

2. **"Access Denied" errors**:
   - Verify the S3 bucket name in the policy matches your actual bucket
   - Check that Bedrock model ARNs in the policy match the models you're using
   - Ensure the region in your environment variables matches the policy resources

3. **Application still using access keys**:
   - Verify that AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY are removed from environment
   - Restart the application services after removing credentials
   - Check application logs for authentication method being used

### Verification Commands

Test IAM role authentication from your EC2 instance:

```bash
# Test S3 access
aws s3 ls s3://your-bucket-name

# Test Bedrock access (requires AWS CLI v2)
aws bedrock list-foundation-models --region ap-south-1

# Test STS (to see current identity)
aws sts get-caller-identity
```

## Security Best Practices

1. **Principle of Least Privilege**: Only grant the minimum permissions required
2. **Resource-Specific Policies**: Limit access to specific S3 buckets and Bedrock models
3. **Regular Auditing**: Periodically review and audit IAM policies and roles
4. **Monitoring**: Enable CloudTrail to monitor API calls made using the IAM role
5. **Rotation**: IAM roles don't require credential rotation, but review policies regularly

## Migration from Access Keys

If you're migrating from access key authentication:

1. **Test in Development**: First test IAM role authentication in a development environment
2. **Gradual Migration**: Keep access keys as fallback during initial testing
3. **Monitor Logs**: Watch application logs during migration to ensure smooth transition
4. **Remove Credentials**: Only remove access keys after confirming IAM role authentication works
5. **Update Documentation**: Update any deployment or configuration documentation

## Next Steps

After completing the IAM role setup:

1. Test your RAG application functionality
2. Verify document ingestion from S3 works
3. Test query processing with Bedrock models
4. Monitor application logs for any authentication issues
5. Update your deployment automation to use IAM roles

For additional support, refer to the AWS IAM documentation or contact your AWS support team.
